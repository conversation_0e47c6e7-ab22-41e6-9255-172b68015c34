// Code generated by templ - DO NOT EDIT.

// templ: version: v0.3.924
package components

//lint:file-ignore SA4006 This context is only used if a nested component is present.

import "github.com/a-h/templ"
import templruntime "github.com/a-h/templ/runtime"

func ConfirmModal() templ.Component {
	return templruntime.GeneratedTemplate(func(templ_7745c5c3_Input templruntime.GeneratedComponentInput) (templ_7745c5c3_Err error) {
		templ_7745c5c3_W, ctx := templ_7745c5c3_Input.Writer, templ_7745c5c3_Input.Context
		if templ_7745c5c3_CtxErr := ctx.Err(); templ_7745c5c3_CtxErr != nil {
			return templ_7745c5c3_CtxErr
		}
		templ_7745c5c3_Buffer, templ_7745c5c3_IsBuffer := templruntime.GetBuffer(templ_7745c5c3_W)
		if !templ_7745c5c3_IsBuffer {
			defer func() {
				templ_7745c5c3_BufErr := templruntime.ReleaseBuffer(templ_7745c5c3_Buffer)
				if templ_7745c5c3_Err == nil {
					templ_7745c5c3_Err = templ_7745c5c3_BufErr
				}
			}()
		}
		ctx = templ.InitializeContext(ctx)
		templ_7745c5c3_Var1 := templ.GetChildren(ctx)
		if templ_7745c5c3_Var1 == nil {
			templ_7745c5c3_Var1 = templ.NopComponent
		}
		ctx = templ.ClearChildren(ctx)
		templ_7745c5c3_Err = templruntime.WriteString(templ_7745c5c3_Buffer, 1, "<!-- 确认弹窗模态框 --><div id=\"confirmModal\" class=\"fixed inset-0 z-50 hidden\" style=\"display: none;\"><!-- 背景遮罩 --><div class=\"fixed inset-0 bg-black bg-opacity-50 transition-opacity duration-300\" onclick=\"closeConfirmModal()\"></div><!-- 弹窗内容 --><div class=\"fixed inset-0 flex items-center justify-center p-4\"><div class=\"bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full transform transition-all duration-300 scale-95 opacity-0\" id=\"confirmModalContent\"><!-- 弹窗头部 --><div class=\"flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700\"><div class=\"flex items-center\"><div class=\"flex-shrink-0 w-10 h-10 mx-auto bg-red-100 dark:bg-red-900/20 rounded-full flex items-center justify-center\"><svg class=\"w-6 h-6 text-red-600 dark:text-red-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\"><path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z\"></path></svg></div><div class=\"ml-4\"><h3 class=\"text-lg font-medium text-gray-900 dark:text-white\" id=\"confirmModalTitle\">确认操作</h3></div></div><button onclick=\"closeConfirmModal()\" class=\"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors\"><svg class=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\"><path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M6 18L18 6M6 6l12 12\"></path></svg></button></div><!-- 弹窗内容 --><div class=\"p-6\"><p class=\"text-sm text-gray-500 dark:text-gray-400\" id=\"confirmModalMessage\">您确定要执行此操作吗？</p></div><!-- 弹窗按钮 --><div class=\"flex items-center justify-end space-x-3 p-6 border-t border-gray-200 dark:border-gray-700\"><button onclick=\"closeConfirmModal()\" class=\"px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors\">取消</button> <button onclick=\"confirmModalAction()\" class=\"px-4 py-2 text-sm font-medium text-white bg-red-600 border border-transparent rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors\" id=\"confirmModalButton\">确认</button></div></div></div></div><script>\n\t\tlet confirmModalCallback = null;\n\t\t\n\t\t// 显示确认弹窗\n\t\tfunction showConfirmModal(title, message, confirmText = '确认', callback = null) {\n\t\t\tconst modal = document.getElementById('confirmModal');\n\t\t\tconst content = document.getElementById('confirmModalContent');\n\t\t\tconst titleEl = document.getElementById('confirmModalTitle');\n\t\t\tconst messageEl = document.getElementById('confirmModalMessage');\n\t\t\tconst buttonEl = document.getElementById('confirmModalButton');\n\t\t\t\n\t\t\t// 设置内容\n\t\t\ttitleEl.textContent = title;\n\t\t\tmessageEl.textContent = message;\n\t\t\tbuttonEl.textContent = confirmText;\n\t\t\t\n\t\t\t// 设置回调\n\t\t\tconfirmModalCallback = callback;\n\t\t\t\n\t\t\t// 显示弹窗\n\t\t\tmodal.classList.remove('hidden');\n\t\t\t\n\t\t\t// 动画效果\n\t\t\tsetTimeout(() => {\n\t\t\t\tcontent.classList.remove('scale-95', 'opacity-0');\n\t\t\t\tcontent.classList.add('scale-100', 'opacity-100');\n\t\t\t}, 10);\n\t\t}\n\t\t\n\t\t// 关闭确认弹窗\n\t\tfunction closeConfirmModal() {\n\t\t\tconst modal = document.getElementById('confirmModal');\n\t\t\tconst content = document.getElementById('confirmModalContent');\n\t\t\t\n\t\t\t// 动画效果\n\t\t\tcontent.classList.remove('scale-100', 'opacity-100');\n\t\t\tcontent.classList.add('scale-95', 'opacity-0');\n\t\t\t\n\t\t\tsetTimeout(() => {\n\t\t\t\tmodal.classList.add('hidden');\n\t\t\t\tconfirmModalCallback = null;\n\t\t\t}, 300);\n\t\t}\n\t\t\n\t\t// 确认操作\n\t\tfunction confirmModalAction() {\n\t\t\tif (confirmModalCallback) {\n\t\t\t\tconfirmModalCallback();\n\t\t\t}\n\t\t\tcloseConfirmModal();\n\t\t}\n\t\t\n\t\t// ESC 键关闭弹窗\n\t\tdocument.addEventListener('keydown', function(e) {\n\t\t\tif (e.key === 'Escape' && !document.getElementById('confirmModal').classList.contains('hidden')) {\n\t\t\t\tcloseConfirmModal();\n\t\t\t}\n\t\t});\n\t</script>")
		if templ_7745c5c3_Err != nil {
			return templ_7745c5c3_Err
		}
		return nil
	})
}

var _ = templruntime.GeneratedTemplate
