package components

import "server-panel/internal/i18n"

templ MemoryInfo(currentLang string) {
	<!-- 内存详细信息 -->
	<div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
		<div class="flex items-center justify-between mb-4">
			<h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">{ i18n.T(currentLang, "system.memory.details") }</h3>
			<span id="memory-usage-detail" class="text-sm text-gray-600 dark:text-gray-400">--</span>
		</div>
		<div id="memory-details" class="space-y-4">
			<div class="animate-pulse">
				<div class="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4 mb-2"></div>
				<div class="h-2 bg-gray-200 dark:bg-gray-700 rounded mb-4"></div>
				<div class="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/2 mb-2"></div>
				<div class="h-4 bg-gray-200 dark:bg-gray-700 rounded w-2/3"></div>
			</div>
		</div>
	</div>
}
