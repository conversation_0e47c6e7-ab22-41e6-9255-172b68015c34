package components

import "server-panel/internal/i18n"

templ CPUCard(currentLang string) {
	<!-- CPU卡片 -->
	<div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 hover:shadow-md transition-shadow">
		<div class="flex items-center justify-between">
			<div>
				<p class="text-sm font-medium text-gray-600 dark:text-gray-400">{ i18n.T(currentLang, "system.cpu.usage") }</p>
				<p id="cpu-usage" class="text-2xl font-bold text-blue-600 dark:text-blue-400">--</p>
			</div>
			<div class="w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center">
				<svg class="w-6 h-6 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
					<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z"></path>
				</svg>
			</div>
		</div>
		<div class="mt-4">
			<div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
				<div id="cpu-progress" class="bg-blue-600 dark:bg-blue-500 h-2 rounded-full transition-all duration-500" style="width: 0%"></div>
			</div>
		</div>
	</div>
}
