package components

import (
	"server-panel/internal/projects"
	"fmt"
)

func formatBytes(bytes int64) string {
	const unit = 1024
	if bytes < unit {
		return fmt.Sprintf("%d B", bytes)
	}
	div, exp := int64(unit), 0
	for n := bytes / unit; n >= unit; n /= unit {
		div *= unit
		exp++
	}
	return fmt.Sprintf("%.1f %cB", float64(bytes)/float64(div), "KMGTPE"[exp])
}

templ ProjectCard(project projects.Project) {
	<div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 hover:shadow-md transition-shadow">
		<div class="flex items-center justify-between mb-4">
			<div class="flex items-center">
				<div class="w-10 h-10 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center mr-3">
					<svg class="w-6 h-6 text-blue-600 dark:text-blue-400" fill="currentColor" viewBox="0 0 20 20">
						<path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"></path>
					</svg>
				</div>
				<div>
					<h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">{ project.Name }</h3>
					<p class="text-sm text-gray-500 dark:text-gray-400">{ project.Domain }</p>
				</div>
			</div>
			<div class="flex flex-col items-end">
				if project.Status == projects.StatusActive {
					<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
						<svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
							<path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
						</svg>
						Active
					</span>
				} else {
					<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
						Inactive
					</span>
				}
			</div>
		</div>

		<div class="space-y-2 mb-4">
			<div class="flex justify-between text-sm">
				<span class="text-gray-500">Path:</span>
				<span class="text-gray-900 font-mono text-xs">{ project.Path }</span>
			</div>
			<div class="flex justify-between text-sm">
				<span class="text-gray-500">Size:</span>
				<span class="text-gray-900">{ formatBytes(project.Size) }</span>
			</div>
			<div class="flex justify-between text-sm">
				<span class="text-gray-500">Created:</span>
				<span class="text-gray-900">{ project.CreatedAt.Format("2006-01-02") }</span>
			</div>
			if project.DatabaseName != "" {
				<div class="flex justify-between text-sm">
					<span class="text-gray-500">Database:</span>
					<span class="text-gray-900">{ project.DatabaseName }</span>
				</div>
			}
		</div>

		<!-- Action Buttons -->
		<div class="flex space-x-2">
			<div class="relative" x-data="{ open: false }">
				<button 
					@click="open = !open"
					class="flex-1 bg-blue-600 text-white px-3 py-2 rounded-md text-sm font-medium hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 flex items-center justify-center">
					<svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
					</svg>
					Manage
					<svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
					</svg>
				</button>
				
				<!-- Dropdown Menu -->
				<div x-show="open" @click.away="open = false" class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg z-10 border border-gray-200">
					<div class="py-1">
						<a href="#" 
							class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
							data-project={ project.ID }
							data-action="configure">
							<svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
							</svg>
							Configure
						</a>
						<a href="#" 
							class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
							data-project={ project.ID }
							data-action="files">
							<svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-5l-2-2H5a2 2 0 00-2 2z"></path>
							</svg>
							File Manager
						</a>
						<a href="#" 
							class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
							data-project={ project.ID }
							data-action="backup">
							<svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10"></path>
							</svg>
							Backup
						</a>
						<div class="border-t border-gray-100"></div>
						<a href="#" 
							class="block px-4 py-2 text-sm text-red-700 hover:bg-red-50"
							data-project={ project.ID }
							data-action="delete">
							<svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
							</svg>
							Delete
						</a>
					</div>
				</div>
			</div>
		</div>
	</div>

	<script>
		// Use event delegation for project actions
		document.addEventListener('click', function(e) {
			if (e.target.dataset.project && e.target.dataset.action) {
				e.preventDefault();
				const projectId = e.target.dataset.project;
				const action = e.target.dataset.action;
				
				switch(action) {
					case 'configure':
						window.location.href = `/projects/${projectId}/configure`;
						break;
					case 'files':
						window.location.href = `/projects/${projectId}/files`;
						break;
					case 'backup':
						window.location.href = `/projects/${projectId}/backup`;
						break;
					case 'delete':
						deleteProject(projectId);
						break;
				}
			}
		});

		function deleteProject(projectId) {
			if (confirm(`Are you sure you want to delete project ${projectId}? This action cannot be undone and will also delete the associated database.`)) {
				fetch(`/api/projects/${projectId}/delete`, {
					method: 'DELETE',
					headers: {
						'Content-Type': 'application/json',
					}
				})
				.then(response => response.json())
				.then(data => {
					if (data.success) {
						location.reload();
					} else {
						alert('Failed to delete project: ' + data.error);
					}
				})
				.catch(error => {
					alert('Failed to delete project: ' + error.message);
				});
			}
		}
	</script>
}
