package components

import "server-panel/internal/i18n"

templ MemoryCard(currentLang string) {
	<!-- 内存卡片 -->
	<div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 hover:shadow-md transition-shadow">
		<div class="flex items-center justify-between">
			<div>
				<p class="text-sm font-medium text-gray-600 dark:text-gray-400">{ i18n.T(currentLang, "system.memory.usage") }</p>
				<p id="memory-usage" class="text-2xl font-bold text-green-600 dark:text-green-400">--</p>
			</div>
			<div class="w-12 h-12 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center">
				<svg class="w-6 h-6 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
					<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4"></path>
				</svg>
			</div>
		</div>
		<div class="mt-4">
			<div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
				<div id="memory-progress" class="bg-green-600 dark:bg-green-500 h-2 rounded-full transition-all duration-500" style="width: 0%"></div>
			</div>
		</div>
	</div>
}
