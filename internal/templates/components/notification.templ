package components

templ Notification() {
	<!-- 通知容器 -->
	<div id="notificationContainer" class="fixed top-4 right-4 z-50 space-y-2">
		<!-- 通知会动态添加到这里 -->
	</div>
	
	<script>
		// 显示通知
		function showNotification(message, type = 'info', duration = 5000) {
			const container = document.getElementById('notificationContainer');
			const notificationId = 'notification-' + Date.now();
			
			// 确定通知样式
			let bgColor, textColor, iconSvg;
			switch(type) {
				case 'success':
					bgColor = 'bg-green-500';
					textColor = 'text-white';
					iconSvg = `<svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
						<path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
					</svg>`;
					break;
				case 'error':
					bgColor = 'bg-red-500';
					textColor = 'text-white';
					iconSvg = `<svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
						<path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
					</svg>`;
					break;
				case 'warning':
					bgColor = 'bg-yellow-500';
					textColor = 'text-white';
					iconSvg = `<svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
						<path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
					</svg>`;
					break;
				default: // info
					bgColor = 'bg-blue-500';
					textColor = 'text-white';
					iconSvg = `<svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
						<path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
					</svg>`;
			}
			
			// 创建通知元素
			const notification = document.createElement('div');
			notification.id = notificationId;
			notification.className = `${bgColor} ${textColor} px-4 py-3 rounded-lg shadow-lg flex items-center space-x-3 min-w-80 max-w-md transform transition-all duration-300 translate-x-full opacity-0`;
			notification.innerHTML = `
				<div class="flex-shrink-0">
					${iconSvg}
				</div>
				<div class="flex-1">
					<p class="text-sm font-medium">${message}</p>
				</div>
				<button onclick="removeNotification('${notificationId}')" class="flex-shrink-0 ml-2 hover:opacity-75 transition-opacity">
					<svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
						<path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
					</svg>
				</button>
			`;
			
			// 添加到容器
			container.appendChild(notification);
			
			// 显示动画
			setTimeout(() => {
				notification.classList.remove('translate-x-full', 'opacity-0');
				notification.classList.add('translate-x-0', 'opacity-100');
			}, 10);
			
			// 自动移除
			if (duration > 0) {
				setTimeout(() => {
					removeNotification(notificationId);
				}, duration);
			}
		}
		
		// 移除通知
		function removeNotification(notificationId) {
			const notification = document.getElementById(notificationId);
			if (notification) {
				notification.classList.remove('translate-x-0', 'opacity-100');
				notification.classList.add('translate-x-full', 'opacity-0');
				
				setTimeout(() => {
					if (notification.parentNode) {
						notification.parentNode.removeChild(notification);
					}
				}, 300);
			}
		}
		
		// 清除所有通知
		function clearAllNotifications() {
			const container = document.getElementById('notificationContainer');
			const notifications = container.querySelectorAll('[id^="notification-"]');
			notifications.forEach(notification => {
				removeNotification(notification.id);
			});
		}
	</script>
}
