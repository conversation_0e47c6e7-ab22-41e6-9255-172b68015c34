// Code generated by templ - DO NOT EDIT.

// templ: version: v0.3.924
package layouts

//lint:file-ignore SA4006 This context is only used if a nested component is present.

import "github.com/a-h/templ"
import templruntime "github.com/a-h/templ/runtime"

import "server-panel/internal/templates/components"

func Base(title string, username string, currentLang string) templ.Component {
	return templruntime.GeneratedTemplate(func(templ_7745c5c3_Input templruntime.GeneratedComponentInput) (templ_7745c5c3_Err error) {
		templ_7745c5c3_W, ctx := templ_7745c5c3_Input.Writer, templ_7745c5c3_Input.Context
		if templ_7745c5c3_CtxErr := ctx.Err(); templ_7745c5c3_CtxErr != nil {
			return templ_7745c5c3_CtxErr
		}
		templ_7745c5c3_Buffer, templ_7745c5c3_IsBuffer := templruntime.GetBuffer(templ_7745c5c3_W)
		if !templ_7745c5c3_IsBuffer {
			defer func() {
				templ_7745c5c3_BufErr := templruntime.ReleaseBuffer(templ_7745c5c3_Buffer)
				if templ_7745c5c3_Err == nil {
					templ_7745c5c3_Err = templ_7745c5c3_BufErr
				}
			}()
		}
		ctx = templ.InitializeContext(ctx)
		templ_7745c5c3_Var1 := templ.GetChildren(ctx)
		if templ_7745c5c3_Var1 == nil {
			templ_7745c5c3_Var1 = templ.NopComponent
		}
		ctx = templ.ClearChildren(ctx)
		templ_7745c5c3_Err = templruntime.WriteString(templ_7745c5c3_Buffer, 1, "<!doctype html><html lang=\"zh-CN\" x-data=\"app\" x-init=\"init()\" :class=\"{ 'dark': isDark }\"><head><meta charset=\"UTF-8\"><meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\"><title>")
		if templ_7745c5c3_Err != nil {
			return templ_7745c5c3_Err
		}
		var templ_7745c5c3_Var2 string
		templ_7745c5c3_Var2, templ_7745c5c3_Err = templ.JoinStringErrs(title)
		if templ_7745c5c3_Err != nil {
			return templ.Error{Err: templ_7745c5c3_Err, FileName: `internal/templates/layouts/base.templ`, Line: 11, Col: 16}
		}
		_, templ_7745c5c3_Err = templ_7745c5c3_Buffer.WriteString(templ.EscapeString(templ_7745c5c3_Var2))
		if templ_7745c5c3_Err != nil {
			return templ_7745c5c3_Err
		}
		templ_7745c5c3_Err = templruntime.WriteString(templ_7745c5c3_Buffer, 2, "</title><!-- 防止暗黑模式闪白 --><script>\n\t\t\t// 在页面加载前立即应用暗黑模式\n\t\t\t(function() {\n\t\t\t\tconst theme = localStorage.getItem('theme') || 'dark';\n\t\t\t\tif (theme === 'dark') {\n\t\t\t\t\tdocument.documentElement.classList.add('dark');\n\t\t\t\t}\n\t\t\t})();\n\t\t</script><!-- CSS --><link rel=\"stylesheet\" href=\"/static/css/output.css?v=20250727-sse-debug\"><link rel=\"icon\" type=\"image/svg+xml\" href=\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🖥️</text></svg>\"></head><body class=\"bg-gray-50 font-sans antialiased dark:bg-gray-900 transition-colors duration-200\">")
		if templ_7745c5c3_Err != nil {
			return templ_7745c5c3_Err
		}
		templ_7745c5c3_Err = components.Navigation(username, currentLang).Render(ctx, templ_7745c5c3_Buffer)
		if templ_7745c5c3_Err != nil {
			return templ_7745c5c3_Err
		}
		templ_7745c5c3_Err = templruntime.WriteString(templ_7745c5c3_Buffer, 3, "<!-- 主内容区域 --><main class=\"pt-16\"><div class=\"max-w-7xl mx-auto py-6 sm:px-6 lg:px-8\">")
		if templ_7745c5c3_Err != nil {
			return templ_7745c5c3_Err
		}
		templ_7745c5c3_Err = templ_7745c5c3_Var1.Render(ctx, templ_7745c5c3_Buffer)
		if templ_7745c5c3_Err != nil {
			return templ_7745c5c3_Err
		}
		templ_7745c5c3_Err = templruntime.WriteString(templ_7745c5c3_Buffer, 4, "</div></main>")
		if templ_7745c5c3_Err != nil {
			return templ_7745c5c3_Err
		}
		templ_7745c5c3_Err = components.Modal().Render(ctx, templ_7745c5c3_Buffer)
		if templ_7745c5c3_Err != nil {
			return templ_7745c5c3_Err
		}
		templ_7745c5c3_Err = templruntime.WriteString(templ_7745c5c3_Buffer, 5, "<!-- JavaScript --><script src=\"/static/js/htmx.min.js?v=working\"></script><!-- Alpine.js 预初始化脚本 --><script>\n\t\t\t// 在 Alpine.js 加载前设置初始状态，防止闪烁\n\t\t\twindow.Alpine = window.Alpine || {};\n\t\t\twindow.Alpine.start = window.Alpine.start || function() {};\n\n\t\t\t// 添加全局样式来隐藏未初始化的组件\n\t\t\tconst style = document.createElement('style');\n\t\t\tstyle.textContent = `\n\t\t\t\t/* 确保所有 Alpine.js 组件在初始化前都隐藏 */\n\t\t\t\t[x-data]:not(.alpine-initialized) [x-show] { display: none !important; }\n\t\t\t\t[x-show=\"false\"] { display: none !important; }\n\t\t\t\t#progress-bar.hidden { display: none !important; }\n\n\t\t\t\t/* 语言选择器特殊处理 */\n\t\t\t\t[x-data] .absolute[x-show] { display: none !important; }\n\t\t\t\t[x-data] .absolute[x-show=\"true\"] { display: block !important; }\n\t\t\t`;\n\t\t\tdocument.head.appendChild(style);\n\n\t\t\t// 确保进度条初始隐藏\n\t\t\tdocument.addEventListener('DOMContentLoaded', function() {\n\t\t\t\tconst progressBar = document.getElementById('progress-bar');\n\t\t\t\tif (progressBar) {\n\t\t\t\t\tprogressBar.style.display = 'none';\n\t\t\t\t\tprogressBar.classList.add('hidden');\n\t\t\t\t}\n\t\t\t});\n\t\t</script><script src=\"/static/js/alpine.min.js?v=working\" defer></script><script>\n\t\t\t// DigWis Panel v3.6 - 原生Go版本 (手动测试按钮)\n\t\t\tconsole.log('🚀 DigWis Panel v3.6 加载中 - 手动测试按钮!');\n\n\t\t\t// Alpine.js 全局状态\n\t\t\tdocument.addEventListener('alpine:init', () => {\n\t\t\t\tAlpine.data('app', () => ({\n\t\t\t\t\tmodalOpen: false,\n\t\t\t\t\tisDark: localStorage.getItem('theme') === 'dark' ||\n\t\t\t\t\t\t\t(!localStorage.getItem('theme') && window.matchMedia('(prefers-color-scheme: dark)').matches),\n\n\t\t\t\t\tinit() {\n\t\t\t\t\t\tconsole.log('DigWis Panel 已加载');\n\t\t\t\t\t\t// 初始化主题\n\t\t\t\t\t\tthis.updateTheme();\n\t\t\t\t\t\t// 标记 Alpine.js 已初始化\n\t\t\t\t\t\tthis.$el.classList.add('alpine-initialized');\n\t\t\t\t\t},\n\n\t\t\t\t\ttoggleTheme() {\n\t\t\t\t\t\tthis.isDark = !this.isDark;\n\t\t\t\t\t\tthis.updateTheme();\n\t\t\t\t\t},\n\n\t\t\t\t\tupdateTheme() {\n\t\t\t\t\t\tif (this.isDark) {\n\t\t\t\t\t\t\tlocalStorage.setItem('theme', 'dark');\n\t\t\t\t\t\t\tdocument.documentElement.classList.add('dark');\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tlocalStorage.setItem('theme', 'light');\n\t\t\t\t\t\t\tdocument.documentElement.classList.remove('dark');\n\t\t\t\t\t\t}\n\t\t\t\t\t},\n\n\t\t\t\t\topenModal(title) {\n\t\t\t\t\t\tdocument.getElementById('modal-title').textContent = title;\n\t\t\t\t\t\tthis.modalOpen = true;\n\t\t\t\t\t\tdocument.getElementById('modal').classList.remove('hidden');\n\t\t\t\t\t},\n\n\t\t\t\t\tcloseModal() {\n\t\t\t\t\t\tthis.modalOpen = false;\n\t\t\t\t\t\tdocument.getElementById('modal').classList.add('hidden');\n\t\t\t\t\t}\n\t\t\t\t}));\n\n\t\t\t\t// 导航状态管理\n\t\t\t\tAlpine.data('navigation', () => ({\n\t\t\t\t\tcurrentPath: window.location.pathname,\n\n\t\t\t\t\tinit() {\n\t\t\t\t\t\t// 监听页面变化（如果使用 HTMX 或其他 SPA 路由）\n\t\t\t\t\t\twindow.addEventListener('popstate', () => {\n\t\t\t\t\t\t\tthis.currentPath = window.location.pathname;\n\t\t\t\t\t\t});\n\t\t\t\t\t},\n\n\t\t\t\t\tsetActiveNav() {\n\t\t\t\t\t\tthis.currentPath = window.location.pathname;\n\t\t\t\t\t},\n\n\t\t\t\t\tisActive(path) {\n\t\t\t\t\t\treturn this.currentPath === path ||\n\t\t\t\t\t\t\t   (path === '/dashboard' && this.currentPath === '/');\n\t\t\t\t\t}\n\t\t\t\t}));\n\t\t\t});\n\n\t\t\t// Alpine.js 初始化完成后的处理\n\t\t\tdocument.addEventListener('alpine:initialized', () => {\n\t\t\t\tconsole.log('Alpine.js 初始化完成');\n\t\t\t\t// 移除防闪烁样式\n\t\t\t\tdocument.querySelectorAll('[x-data]').forEach(el => {\n\t\t\t\t\tel.classList.add('alpine-initialized');\n\t\t\t\t});\n\n\t\t\t\t// 确保进度条保持隐藏\n\t\t\t\tconst progressBar = document.getElementById('progress-bar');\n\t\t\t\tif (progressBar && progressBar.classList.contains('hidden')) {\n\t\t\t\t\tprogressBar.style.display = 'none';\n\t\t\t\t}\n\t\t\t});\n\n\t\t\t// HTMX配置 - 优化版本\n\t\t\thtmx.config.globalViewTransitions = false; // 禁用视图转换以提高性能\n\t\t\thtmx.config.defaultSwapStyle = 'innerHTML';\n\t\t\thtmx.config.defaultSwapDelay = 50; // 减少延迟\n\t\t\thtmx.config.timeout = 10000; // 10秒超时\n\t\t\thtmx.config.historyCacheSize = 5; // 限制历史缓存大小\n\n\t\t\t// SSE相关变量 - 优化版本\n\t\t\tlet eventSource = null;\n\t\t\tlet cpuData = [];\n\t\t\tlet reconnectAttempts = 0;\n\t\t\tconst maxReconnectAttempts = 3; // 减少重连次数\n\t\t\tlet lastUpdateTime = 0;\n\t\t\tconst updateThrottle = 1000; // 限制更新频率为1秒\n\n\t\t\t// 更新连接状态\n\t\t\tfunction updateConnectionStatus(status, text) {\n\t\t\t\tconsole.log('连接状态更新:', status, text);\n\t\t\t\tconst statusEl = document.getElementById('connection-status');\n\t\t\t\tconst textEl = document.getElementById('connection-text');\n\n\t\t\t\tif (statusEl && textEl) {\n\t\t\t\t\tstatusEl.className = 'w-2 h-2 rounded-full ' +\n\t\t\t\t\t\t(status === 'connected' ? 'bg-green-400' :\n\t\t\t\t\t\t status === 'error' ? 'bg-red-400' : 'bg-yellow-400');\n\t\t\t\t\ttextEl.textContent = text;\n\t\t\t\t} else {\n\t\t\t\t\tconsole.log('连接状态元素未找到，状态:', status, text);\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// 更新系统统计 - 优化版本，添加节流机制\n\t\t\tfunction updateSystemStats(stats) {\n\t\t\t\tconst now = Date.now();\n\t\t\t\tif (now - lastUpdateTime < updateThrottle) {\n\t\t\t\t\treturn; // 跳过过于频繁的更新\n\t\t\t\t}\n\t\t\t\tlastUpdateTime = now;\n\n\t\t\t\tconsole.log('更新系统统计，数据结构:', stats);\n\n\t\t\t\t// 批量更新DOM，减少重排重绘\n\t\t\t\trequestAnimationFrame(() => {\n\t\t\t\t\tupdateCPUCard(stats.cpu);\n\t\t\t\t\tupdateMemoryCard(stats.memory);\n\t\t\t\t\tupdateDiskCard(stats.disk);\n\t\t\t\t\tupdateNetworkCard(stats.network);\n\t\t\t\t\tupdateCPUChart(stats.cpu ? stats.cpu.usage : 0);\n\t\t\t\t\tupdateMemoryDetails(stats.memory);\n\t\t\t\t});\n\t\t\t}\n\n\t\t\t// 更新CPU卡片\n\t\t\tfunction updateCPUCard(cpu) {\n\t\t\t\tconsole.log('更新CPU卡片:', cpu);\n\t\t\t\tconst usageEl = document.getElementById('cpu-usage');\n\t\t\t\tconst progressEl = document.getElementById('cpu-progress');\n\n\t\t\t\tconsole.log('CPU元素:', usageEl, progressEl);\n\t\t\t\tif (usageEl && cpu && typeof cpu.usage === 'number') {\n\t\t\t\t\tusageEl.textContent = cpu.usage.toFixed(1) + '%';\n\t\t\t\t\tconsole.log('CPU使用率已更新:', cpu.usage.toFixed(1) + '%');\n\t\t\t\t}\n\t\t\t\tif (progressEl && cpu && typeof cpu.usage === 'number') {\n\t\t\t\t\tprogressEl.style.width = cpu.usage.toFixed(1) + '%';\n\t\t\t\t\tconsole.log('CPU进度条已更新:', cpu.usage.toFixed(1) + '%');\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// 更新内存卡片\n\t\t\tfunction updateMemoryCard(memory) {\n\t\t\t\tconst usageEl = document.getElementById('memory-usage');\n\t\t\t\tconst progressEl = document.getElementById('memory-progress');\n\n\t\t\t\tif (usageEl && memory && typeof memory.usage === 'number') {\n\t\t\t\t\tusageEl.textContent = memory.usage.toFixed(1) + '%';\n\t\t\t\t}\n\t\t\t\tif (progressEl && memory && typeof memory.usage === 'number') {\n\t\t\t\t\tprogressEl.style.width = memory.usage.toFixed(1) + '%';\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// 更新磁盘卡片\n\t\t\tfunction updateDiskCard(disk) {\n\t\t\t\tconst usageEl = document.getElementById('disk-usage');\n\t\t\t\tconst progressEl = document.getElementById('disk-progress');\n\n\t\t\t\tif (usageEl && disk && typeof disk.usage === 'number') {\n\t\t\t\t\tusageEl.textContent = disk.usage.toFixed(1) + '%';\n\t\t\t\t}\n\t\t\t\tif (progressEl && disk && typeof disk.usage === 'number') {\n\t\t\t\t\tprogressEl.style.width = disk.usage.toFixed(1) + '%';\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// 更新网络卡片\n\t\t\tfunction updateNetworkCard(network) {\n\t\t\t\tconst usageEl = document.getElementById('network-usage');\n\t\t\t\tconst detailsEl = document.getElementById('network-details');\n\n\t\t\t\tif (usageEl && network) {\n\t\t\t\t\tconst totalBytes = (network.bytes_received || 0) + (network.bytes_sent || 0);\n\t\t\t\t\tusageEl.textContent = formatBytes(totalBytes);\n\t\t\t\t}\n\t\t\t\tif (detailsEl && network) {\n\t\t\t\t\tconst received = formatBytes(network.bytes_received || 0);\n\t\t\t\t\tconst sent = formatBytes(network.bytes_sent || 0);\n\t\t\t\t\tdetailsEl.textContent = '↓ ' + received + ' ↑ ' + sent;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// 更新CPU图表\n\t\t\tfunction updateCPUChart(cpuUsage) {\n\t\t\t\tcpuData.push(cpuUsage);\n\t\t\t\tif (cpuData.length > 30) {\n\t\t\t\t\tcpuData.shift();\n\t\t\t\t}\n\n\t\t\t\tconst canvas = document.getElementById('cpu-canvas');\n\t\t\t\tif (!canvas) return;\n\n\t\t\t\tconst ctx = canvas.getContext('2d');\n\t\t\t\tctx.clearRect(0, 0, canvas.width, canvas.height);\n\n\t\t\t\t// 绘制网格\n\t\t\t\tctx.strokeStyle = '#e5e7eb';\n\t\t\t\tctx.lineWidth = 1;\n\t\t\t\tfor (let i = 0; i <= 10; i++) {\n\t\t\t\t\tconst y = (canvas.height / 10) * i;\n\t\t\t\t\tctx.beginPath();\n\t\t\t\t\tctx.moveTo(0, y);\n\t\t\t\t\tctx.lineTo(canvas.width, y);\n\t\t\t\t\tctx.stroke();\n\t\t\t\t}\n\n\t\t\t\t// 绘制CPU使用率线\n\t\t\t\tif (cpuData.length > 1) {\n\t\t\t\t\tctx.strokeStyle = '#3b82f6';\n\t\t\t\t\tctx.lineWidth = 2;\n\t\t\t\t\tctx.beginPath();\n\n\t\t\t\t\tfor (let i = 0; i < cpuData.length; i++) {\n\t\t\t\t\t\tconst x = (canvas.width / (cpuData.length - 1)) * i;\n\t\t\t\t\t\tconst y = canvas.height - (cpuData[i] / 100) * canvas.height;\n\n\t\t\t\t\t\tif (i === 0) {\n\t\t\t\t\t\t\tctx.moveTo(x, y);\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tctx.lineTo(x, y);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\tctx.stroke();\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// 更新内存详细信息\n\t\t\tfunction updateMemoryDetails(memory) {\n\t\t\t\tconst usageEl = document.getElementById('memory-usage-detail');\n\t\t\t\tconst detailsEl = document.getElementById('memory-details');\n\n\t\t\t\tif (usageEl && memory && typeof memory.usage === 'number') {\n\t\t\t\t\tusageEl.textContent = memory.usage.toFixed(1) + '%';\n\t\t\t\t}\n\n\t\t\t\tif (detailsEl && memory) {\n\t\t\t\t\t// 获取当前语言\n\t\t\t\t\tconst currentLang = getCookie('language') || 'zh';\n\n\t\t\t\t\t// 根据语言设置文本\n\t\t\t\t\tconst labels = {\n\t\t\t\t\t\tzh: {\n\t\t\t\t\t\t\tused: '已使用',\n\t\t\t\t\t\t\tavailable: '可用',\n\t\t\t\t\t\t\tfree: '空闲'\n\t\t\t\t\t\t},\n\t\t\t\t\t\ten: {\n\t\t\t\t\t\t\tused: 'Used',\n\t\t\t\t\t\t\tavailable: 'Available',\n\t\t\t\t\t\t\tfree: 'Free'\n\t\t\t\t\t\t}\n\t\t\t\t\t};\n\n\t\t\t\t\tconst lang = labels[currentLang] || labels.zh;\n\n\t\t\t\t\tconst detailsHTML =\n\t\t\t\t\t\t'<div class=\"space-y-3\">' +\n\t\t\t\t\t\t\t'<div class=\"flex justify-between items-center\">' +\n\t\t\t\t\t\t\t\t'<span class=\"text-sm text-gray-600 dark:text-gray-400\">' + lang.used + '</span>' +\n\t\t\t\t\t\t\t\t'<span class=\"text-sm font-medium text-gray-900 dark:text-gray-100\">' + formatBytes(memory.used || 0) + ' / ' + formatBytes(memory.total || 0) + '</span>' +\n\t\t\t\t\t\t\t'</div>' +\n\t\t\t\t\t\t\t'<div class=\"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2\">' +\n\t\t\t\t\t\t\t\t'<div class=\"bg-green-500 h-2 rounded-full transition-all duration-500\" style=\"width: ' + (memory.usage || 0) + '%\"></div>' +\n\t\t\t\t\t\t\t'</div>' +\n\t\t\t\t\t\t\t'<div class=\"flex justify-between items-center\">' +\n\t\t\t\t\t\t\t\t'<span class=\"text-sm text-gray-600 dark:text-gray-400\">' + lang.available + '</span>' +\n\t\t\t\t\t\t\t\t'<span class=\"text-sm font-medium text-gray-900 dark:text-gray-100\">' + formatBytes(memory.available || 0) + '</span>' +\n\t\t\t\t\t\t\t'</div>' +\n\t\t\t\t\t\t\t'<div class=\"flex justify-between items-center\">' +\n\t\t\t\t\t\t\t\t'<span class=\"text-sm text-gray-600 dark:text-gray-400\">' + lang.free + '</span>' +\n\t\t\t\t\t\t\t\t'<span class=\"text-sm font-medium text-gray-900 dark:text-gray-100\">' + formatBytes(memory.free || 0) + '</span>' +\n\t\t\t\t\t\t\t'</div>' +\n\t\t\t\t\t\t'</div>';\n\t\t\t\t\tdetailsEl.innerHTML = detailsHTML;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// 工具函数\n\t\t\tfunction formatBytes(bytes) {\n\t\t\t\tif (bytes === 0) return '0 B';\n\t\t\t\tconst k = 1024;\n\t\t\t\tconst sizes = ['B', 'KB', 'MB', 'GB', 'TB'];\n\t\t\t\tconst i = Math.floor(Math.log(bytes) / Math.log(k));\n\t\t\t\treturn parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];\n\t\t\t}\n\n\t\t\t// 获取Cookie值\n\t\t\tfunction getCookie(name) {\n\t\t\t\tconst value = `; ${document.cookie}`;\n\t\t\t\tconst parts = value.split(`; ${name}=`);\n\t\t\t\tif (parts.length === 2) return parts.pop().split(';').shift();\n\t\t\t\treturn null;\n\t\t\t}\n\n\t\t\t// 初始化SSE连接 - 标准库实现\n\t\t\tfunction initSSE() {\n\t\t\t\tif (eventSource) {\n\t\t\t\t\tconsole.log('关闭现有SSE连接');\n\t\t\t\t\teventSource.close();\n\t\t\t\t}\n\n\t\t\t\tconsole.log('🔄 正在初始化SSE连接...');\n\t\t\t\tconsole.log('当前页面URL:', window.location.href);\n\t\t\t\tconsole.log('SSE URL:', '/api/sse/stats');\n\t\t\t\tupdateConnectionStatus('connecting', '连接中...');\n\n\t\t\t\t// 创建EventSource连接\n\t\t\t\ttry {\n\t\t\t\t\teventSource = new EventSource('/api/sse/stats');\n\t\t\t\t\tconsole.log('✅ EventSource对象已创建');\n\t\t\t\t\tconsole.log('EventSource readyState:', eventSource.readyState);\n\t\t\t\t\tconsole.log('EventSource URL:', eventSource.url);\n\n\t\t\t\t\t// 立即检查连接状态\n\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\tconsole.log('1秒后 EventSource readyState:', eventSource.readyState);\n\t\t\t\t\t\tconsole.log('EventSource.CONNECTING =', EventSource.CONNECTING);\n\t\t\t\t\t\tconsole.log('EventSource.OPEN =', EventSource.OPEN);\n\t\t\t\t\t\tconsole.log('EventSource.CLOSED =', EventSource.CLOSED);\n\t\t\t\t\t}, 1000);\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('❌ 创建EventSource失败:', error);\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\t// 连接打开事件（标准事件）\n\t\t\t\teventSource.onopen = function(event) {\n\t\t\t\t\tconsole.log('🔗 EventSource onopen 触发:', event);\n\t\t\t\t\tconsole.log('连接状态:', eventSource.readyState);\n\t\t\t\t};\n\n\t\t\t\t// 连接建立事件（自定义事件）\n\t\t\t\teventSource.addEventListener('connected', function(event) {\n\t\t\t\t\tconsole.log('🎉 SSE连接已建立:', event.data);\n\t\t\t\t\tconst currentLang = getCookie('language') || 'zh';\n\t\t\t\t\tconst connectedText = currentLang === 'en' ? 'Connected' : '已连接';\n\t\t\t\t\tupdateConnectionStatus('connected', connectedText);\n\t\t\t\t\treconnectAttempts = 0; // 重置重连计数\n\t\t\t\t});\n\n\t\t\t\t// 系统统计数据事件\n\t\t\t\teventSource.addEventListener('stats', function(event) {\n\t\t\t\t\tconsole.log('📊 收到系统统计数据:', event.data);\n\t\t\t\t\ttry {\n\t\t\t\t\t\tconst stats = JSON.parse(event.data);\n\t\t\t\t\t\tconsole.log('📈 解析后的统计数据:', stats);\n\t\t\t\t\t\tupdateSystemStats(stats);\n\t\t\t\t\t} catch (error) {\n\t\t\t\t\t\tconsole.error('❌ 解析统计数据失败:', error, '原始数据:', event.data);\n\t\t\t\t\t}\n\t\t\t\t});\n\n\t\t\t\t// 心跳包事件 - 保持连接活跃\n\t\t\t\teventSource.addEventListener('heartbeat', function(event) {\n\t\t\t\t\tconsole.log('💓 收到心跳包:', event.data);\n\t\t\t\t\t// 心跳包不需要更新UI，只是保持连接\n\t\t\t\t});\n\n\t\t\t\t// 错误事件\n\t\t\t\teventSource.addEventListener('error', function(event) {\n\t\t\t\t\tconsole.log('SSE错误事件:', event);\n\t\t\t\t\tif (event.data) {\n\t\t\t\t\t\ttry {\n\t\t\t\t\t\t\tconst errorData = JSON.parse(event.data);\n\t\t\t\t\t\t\tconsole.error('SSE错误:', errorData.error);\n\t\t\t\t\t\t\tupdateConnectionStatus('error', '错误: ' + errorData.error);\n\t\t\t\t\t\t} catch (e) {\n\t\t\t\t\t\t\tconsole.error('解析错误数据失败:', e);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t});\n\n\t\t\t\t// 连接错误处理\n\t\t\t\teventSource.onerror = function(event) {\n\t\t\t\t\tconsole.error('SSE连接错误:', event);\n\t\t\t\t\tupdateConnectionStatus('error', '连接错误');\n\n\t\t\t\t\t// 自动重连逻辑\n\t\t\t\t\tif (reconnectAttempts < maxReconnectAttempts) {\n\t\t\t\t\t\treconnectAttempts++;\n\t\t\t\t\t\tconst delay = Math.min(1000 * Math.pow(2, reconnectAttempts), 30000); // 指数退避，最大30秒\n\t\t\t\t\t\tconsole.log(`${delay/1000}秒后尝试第${reconnectAttempts}次重连...`);\n\t\t\t\t\t\tupdateConnectionStatus('connecting', `重连中 (${reconnectAttempts}/${maxReconnectAttempts})`);\n\n\t\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\t\tif (eventSource.readyState === EventSource.CLOSED) {\n\t\t\t\t\t\t\t\tinitSSE();\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}, delay);\n\t\t\t\t\t} else {\n\t\t\t\t\t\tconsole.error('达到最大重连次数，停止重连');\n\t\t\t\t\t\tupdateConnectionStatus('error', '连接失败');\n\t\t\t\t\t}\n\t\t\t\t};\n\n\t\t\t\t// 通用消息处理（兜底）\n\t\t\t\teventSource.onmessage = function(event) {\n\t\t\t\t\tconsole.log('收到SSE消息:', event.data);\n\t\t\t\t\ttry {\n\t\t\t\t\t\tconst data = JSON.parse(event.data);\n\t\t\t\t\t\tif (data.type === 'system_stats') {\n\t\t\t\t\t\t\tupdateSystemStats(data.data);\n\t\t\t\t\t\t}\n\t\t\t\t\t} catch (error) {\n\t\t\t\t\t\tconsole.error('解析SSE数据失败:', error);\n\t\t\t\t\t}\n\t\t\t\t};\n\t\t\t}\n\n\t\t\t// 手动测试SSE连接\n\t\t\tfunction testSSEConnection() {\n\t\t\t\tconsole.log('🧪 手动测试SSE连接...');\n\n\t\t\t\tif (eventSource) {\n\t\t\t\t\teventSource.close();\n\t\t\t\t\tconsole.log('关闭现有连接');\n\t\t\t\t}\n\n\t\t\t\tconst testSource = new EventSource('/api/sse/stats');\n\n\t\t\t\ttestSource.onopen = function(event) {\n\t\t\t\t\tconsole.log('✅ 手动测试 - EventSource连接打开');\n\t\t\t\t\talert('✅ SSE连接成功建立！');\n\t\t\t\t};\n\n\t\t\t\ttestSource.addEventListener('stats', function(event) {\n\t\t\t\t\tconsole.log('📊 手动测试 - 收到数据:', event.data);\n\t\t\t\t\tconst stats = JSON.parse(event.data);\n\t\t\t\t\talert('📊 收到数据！CPU: ' + stats.cpu.usage + '%');\n\t\t\t\t\ttestSource.close();\n\t\t\t\t});\n\n\t\t\t\ttestSource.onerror = function(event) {\n\t\t\t\t\tconsole.log('❌ 手动测试 - 连接错误');\n\t\t\t\t\talert('❌ SSE连接失败！');\n\t\t\t\t};\n\n\t\t\t\t// 10秒后自动关闭\n\t\t\t\tsetTimeout(() => {\n\t\t\t\t\ttestSource.close();\n\t\t\t\t\tconsole.log('🔒 手动测试连接已关闭');\n\t\t\t\t}, 10000);\n\t\t\t}\n\n\t\t\t// 页面加载时初始化\n\t\t\tdocument.addEventListener('DOMContentLoaded', function() {\n\t\t\t\tconsole.log('页面加载完成');\n\t\t\t\tconsole.log('当前页面URL:', window.location.pathname);\n\n\t\t\t\t// 检查所有相关元素\n\t\t\t\tconst cpuElement = document.getElementById('cpu-usage');\n\t\t\t\tconst memoryElement = document.getElementById('memory-usage');\n\t\t\t\tconst diskElement = document.getElementById('disk-usage');\n\t\t\t\tconst networkElement = document.getElementById('network-usage');\n\n\t\t\t\tconsole.log('页面元素检查:');\n\t\t\t\tconsole.log('- CPU元素:', cpuElement);\n\t\t\t\tconsole.log('- 内存元素:', memoryElement);\n\t\t\t\tconsole.log('- 磁盘元素:', diskElement);\n\t\t\t\tconsole.log('- 网络元素:', networkElement);\n\n\t\t\t\t// 如果在仪表板页面，初始化SSE连接\n\t\t\t\tif (cpuElement) {\n\t\t\t\t\tconsole.log('✅ 检测到仪表板页面，初始化SSE连接');\n\t\t\t\t\tinitSSE();\n\t\t\t\t} else {\n\t\t\t\t\tconsole.log('❌ 未检测到仪表板页面');\n\t\t\t\t}\n\t\t\t});\n\n\t\t\t// 页面卸载时关闭SSE连接\n\t\t\twindow.addEventListener('beforeunload', function() {\n\t\t\t\tif (eventSource) {\n\t\t\t\t\tconsole.log('关闭SSE连接');\n\t\t\t\t\teventSource.close();\n\t\t\t\t}\n\t\t\t});\n\t\t</script></body></html>")
		if templ_7745c5c3_Err != nil {
			return templ_7745c5c3_Err
		}
		return nil
	})
}

var _ = templruntime.GeneratedTemplate
