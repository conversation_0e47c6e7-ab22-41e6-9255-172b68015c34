package pages

import (
	"server-panel/internal/templates/layouts"
	"server-panel/internal/i18n"
)
import "server-panel/internal/templates/components"

templ Dashboard(title string, username string, currentLang string) {
	@layouts.Base(title, username, currentLang) {
		<div class="space-y-6">
			<!-- 页面头部 -->
			<div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
				<div class="flex items-center justify-between mb-4">
					<div>
						<h1 class="text-2xl font-bold text-gray-900 dark:text-gray-100">{ i18n.T(currentLang, "dashboard.title") }</h1>
						<p class="mt-1 text-sm text-gray-500 dark:text-gray-400">{ i18n.T(currentLang, "dashboard.subtitle") }</p>
					</div>
					<div class="flex items-center space-x-4">
						<!-- 连接状态指示器 -->
						<div class="flex items-center space-x-2">
							<div id="connection-indicator" class="w-3 h-3 rounded-full bg-gray-400"></div>
							<span id="connection-text" class="text-sm text-gray-600 dark:text-gray-400">连接中...</span>
						</div>
						<!-- 刷新按钮 - 使用 HTMX -->
						<button
							hx-get="/api/dashboard/refresh"
							hx-target="#dashboard-content"
							hx-swap="innerHTML"
							hx-indicator="#refresh-indicator"
							class="inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
							<span id="refresh-indicator" class="htmx-indicator">
								<svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
									<circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
									<path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
								</svg>
							</span>
							🔄 刷新数据
						</button>
					</div>
				</div>
			</div>

			<!-- 主要内容区域 - 使用 HTMX 加载 -->
			<div id="dashboard-content"
				 hx-get="/api/dashboard/content"
				 hx-trigger="load"
				 hx-swap="innerHTML">
				<!-- 加载中状态 -->
				<div class="space-y-6">
					@DashboardSkeleton()
				</div>
			</div>
		</div>
	}
}

// DashboardSkeleton 仪表板骨架屏
templ DashboardSkeleton() {
	<!-- 系统状态卡片骨架 -->
	<div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
		<div class="flex items-center justify-between mb-4">
			<div class="h-6 bg-gray-200 dark:bg-gray-700 rounded w-32 animate-pulse"></div>
		</div>
		<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
			for i := 0; i < 4; i++ {
				<div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 animate-pulse">
					<div class="h-4 bg-gray-200 dark:bg-gray-600 rounded w-20 mb-2"></div>
					<div class="h-8 bg-gray-200 dark:bg-gray-600 rounded w-16 mb-2"></div>
					<div class="h-3 bg-gray-200 dark:bg-gray-600 rounded w-24"></div>
				</div>
			}
		</div>
	</div>

	<!-- 图表区域骨架 -->
	<div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
		for i := 0; i < 2; i++ {
			<div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
				<div class="h-6 bg-gray-200 dark:bg-gray-700 rounded w-32 mb-4 animate-pulse"></div>
				<div class="h-64 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
			</div>
		}
	</div>
}

// DashboardContent 仪表板内容
templ DashboardContent(currentLang string) {
	<!-- 系统状态卡片 -->
	<div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
		<div class="flex items-center justify-between mb-4">
			<h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">{ i18n.T(currentLang, "nav.system") }</h3>
			<span class="text-xs text-gray-500 dark:text-gray-400" id="last-update">
				最后更新: <span hx-get="/api/dashboard/timestamp" hx-trigger="load, every 30s" hx-swap="innerHTML">--</span>
			</span>
		</div>
		<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6"
			 hx-get="/api/dashboard/stats"
			 hx-trigger="load, every 5s"
			 hx-swap="innerHTML">
			@components.CPUCard(currentLang)
			@components.MemoryCard(currentLang)
			@components.DiskCard(currentLang)
			@components.NetworkCard(currentLang)
		</div>
	</div>

	<!-- 图表区域 -->
	<div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
		<div hx-get="/api/dashboard/cpu-chart" hx-trigger="load" hx-swap="innerHTML">
			@components.CPUChart(currentLang)
		</div>
		<div hx-get="/api/dashboard/memory-info" hx-trigger="load" hx-swap="innerHTML">
			@components.MemoryInfo(currentLang)
		</div>
	</div>
}
