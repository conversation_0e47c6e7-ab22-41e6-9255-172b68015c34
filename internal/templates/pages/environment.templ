package pages

import (
	"server-panel/internal/environment"
	"server-panel/internal/templates/layouts"
	"server-panel/internal/templates/components"
	"server-panel/internal/i18n"
)

templ Environment(title, username, currentLang string, overview *environment.EnvironmentOverview) {
	@layouts.Base(title, username, currentLang) {
		<div class="min-h-screen bg-gray-50 dark:bg-gray-900">
			<!-- Welcome Banner for First Time Setup -->
			if overview.FirstTimeSetup {
				<div class="mb-4">
					@components.NotificationBanner(
						"welcome",
						i18n.T(currentLang, "environment.welcome.title"),
						i18n.T(currentLang, "environment.welcome.message"),
						[]components.BannerAction{
							{Text: i18n.T(currentLang, "environment.install.all"), OnClick: "htmx.trigger('#bulk-install-btn', 'click')", Style: "primary"},
							{Text: i18n.T(currentLang, "environment.skip.now"), OnClick: "", Style: "secondary"},
						},
						true,
					)
				</div>

				<!-- Recommended Setup List -->
				if len(overview.RecommendedSetup) > 0 {
					<div class="bg-blue-50 dark:bg-blue-900/20 border-l-4 border-blue-400 dark:border-blue-500 p-4 mb-6">
						<div class="flex">
							<div class="flex-shrink-0">
								<svg class="h-5 w-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
									<path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
								</svg>
							</div>
							<div class="ml-3">
								<h3 class="text-sm font-medium text-blue-800 dark:text-blue-200">Recommended Environment Stack</h3>
								<div class="mt-2 text-sm text-blue-700 dark:text-blue-300">
									<div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2">
										for _, service := range overview.RecommendedSetup {
											<div class="flex items-center">
												<svg class="w-4 h-4 mr-2 text-green-500" fill="currentColor" viewBox="0 0 20 20">
													<path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
												</svg>
												<span class="text-sm">{ service }</span>
											</div>
										}
									</div>
								</div>
							</div>
						</div>
					</div>
				}
			}

			<!-- Header -->
			<div class="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
				<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
					<div class="flex justify-between items-center py-6">
						<div>
							<h1 class="text-3xl font-bold text-gray-900 dark:text-gray-100">{ i18n.T(currentLang, "environment.title") }</h1>
							<p class="mt-1 text-sm text-gray-500 dark:text-gray-400">{ i18n.T(currentLang, "environment.subtitle") }</p>
						</div>
						<div class="flex space-x-3">
							<!-- 批量安装按钮 -->
							<button
								id="bulk-install-btn"
								hx-post="/api/environment/bulk-install"
								hx-target="#environment-content"
								hx-swap="innerHTML"
								hx-indicator="#bulk-install-indicator"
								class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
								<span id="bulk-install-indicator" class="htmx-indicator">
									<svg class="animate-spin w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24">
										<circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
										<path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
									</svg>
								</span>
								<svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
								</svg>
								批量安装
							</button>
							<!-- 刷新按钮 -->
							<button
								hx-get="/api/environment/refresh"
								hx-target="#environment-content"
								hx-swap="innerHTML"
								hx-indicator="#refresh-indicator"
								class="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
								<span id="refresh-indicator" class="htmx-indicator">
									<svg class="animate-spin w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24">
										<circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
										<path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
									</svg>
								</span>
								<svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
								</svg>
								{ i18n.T(currentLang, "environment.refresh") }
							</button>
						</div>
					</div>
				</div>
			</div>

			<!-- Main Content -->
			<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
				<!-- Progress Bar (使用 HTMX 控制显示) -->
				<div id="progress-bar" class="mb-6 hidden" style="display: none !important;">
					<div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
						<div class="flex items-center justify-between mb-2">
							<h3 id="progress-title" class="text-lg font-medium text-gray-900 dark:text-gray-100">Installing...</h3>
							<span id="progress-percent" class="text-sm text-gray-500 dark:text-gray-400">0%</span>
						</div>
						<div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
							<div id="progress-fill" class="bg-blue-600 h-2 rounded-full transition-all duration-300" style="width: 0%"></div>
						</div>
						<p id="progress-message" class="mt-2 text-sm text-gray-600 dark:text-gray-400">Preparing...</p>
					</div>
				</div>

				<!-- Environment Content -->
				<div id="environment-content"
					 hx-get="/api/environment/content"
					 hx-trigger="load"
					 hx-swap="innerHTML">
					@EnvironmentContent(overview, currentLang)
				</div>
			</div>
		</div>

		<!-- 进度跟踪脚本 (纯 JavaScript) -->
		<script>
			// HTMX 事件监听器
			document.addEventListener('htmx:afterRequest', function(event) {
				if (event.detail.xhr.status === 202) {
					// 开始进度跟踪
					startProgressTracking();
				}
			});

			function startProgressTracking() {
				const progressBar = document.getElementById('progress-bar');
				const progressFill = document.getElementById('progress-fill');
				const progressPercent = document.getElementById('progress-percent');
				const progressMessage = document.getElementById('progress-message');

				// 显示进度条
				progressBar.classList.remove('hidden');

				// 轮询进度更新
				const interval = setInterval(() => {
					fetch('/api/environment/progress')
						.then(response => response.json())
						.then(data => {
							if (data.success && data.data) {
								const progress = data.data.progress;
								progressFill.style.width = progress + '%';
								progressPercent.textContent = progress + '%';
								progressMessage.textContent = data.data.message;

								if (data.data.status === 'completed') {
									clearInterval(interval);
									progressBar.classList.add('hidden');
									// 刷新环境内容
									htmx.trigger('#environment-content', 'refresh');
								} else if (data.data.status === 'error') {
									clearInterval(interval);
									progressBar.classList.add('hidden');
									alert('Error: ' + data.data.message);
								}
							}
						})
						.catch(error => {
							clearInterval(interval);
							progressBar.classList.add('hidden');
							alert('Failed to track progress: ' + error.message);
						});
				}, 1000);
			}
		</script>

		<!-- 自定义确认弹窗 -->
		@components.ConfirmModal()

		<!-- 通知系统 -->
		@components.Notification()
	}
}

// EnvironmentContent 环境内容组件
templ EnvironmentContent(overview *environment.EnvironmentOverview, currentLang string) {
	<!-- Environment Cards -->
	<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
		for _, service := range overview.Services {
			@components.EnvironmentCard(service, currentLang)
		}
	</div>

	<!-- PHP Extensions (if PHP is installed) -->
	if len(overview.PHPExtensions) > 0 {
		<div id="php-extensions-section" class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 transition-all duration-300">
			<div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
				<h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">PHP Extensions</h3>
				<p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Manage PHP extensions and modules</p>
			</div>
			<div class="p-6">
				<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
					for _, ext := range overview.PHPExtensions {
						@components.PHPExtensionCard(ext)
					}
				</div>
			</div>
		</div>
	}
}
