package pages

templ Login(title string, errorMsg string) {
	<!DOCTYPE html>
	<html lang="zh-CN">
	<head>
		<meta charset="UTF-8"/>
		<meta name="viewport" content="width=device-width, initial-scale=1.0"/>
		<title>{ title }</title>
		<link rel="stylesheet" href="/static/css/output.css"/>
		<link rel="icon" type="image/svg+xml" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🖥️</text></svg>"/>
	</head>
	<body class="bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
		<div class="sm:mx-auto sm:w-full sm:max-w-md">
			<div class="flex justify-center">
				<div class="w-12 h-12 bg-blue-600 rounded-lg flex items-center justify-center">
					<span class="text-white font-bold text-xl">D</span>
				</div>
			</div>
			<h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
				登录到 DigWis Panel
			</h2>
			<p class="mt-2 text-center text-sm text-gray-600">
				使用您的系统账户登录
			</p>
		</div>

		<div class="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
			<div class="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
				if errorMsg != "" {
					<div class="mb-4 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded relative">
						<span class="block sm:inline">{ errorMsg }</span>
					</div>
				}

				<form class="space-y-6" action="/login" method="POST">
					<div>
						<label for="username" class="block text-sm font-medium text-gray-700">
							用户名
						</label>
						<div class="mt-1">
							<input id="username" name="username" type="text" autocomplete="username" required 
								   class="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
								   placeholder="输入用户名"/>
						</div>
					</div>

					<div>
						<label for="password" class="block text-sm font-medium text-gray-700">
							密码
						</label>
						<div class="mt-1">
							<input id="password" name="password" type="password" autocomplete="current-password" required 
								   class="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
								   placeholder="输入密码"/>
						</div>
					</div>

					<div class="flex items-center justify-between">
						<div class="flex items-center">
							<input id="remember-me" name="remember-me" type="checkbox" 
								   class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"/>
							<label for="remember-me" class="ml-2 block text-sm text-gray-900">
								记住我
							</label>
						</div>
					</div>

					<div>
						<button type="submit" 
								class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
							<span class="absolute left-0 inset-y-0 flex items-center pl-3">
								<svg class="h-5 w-5 text-blue-500 group-hover:text-blue-400" fill="currentColor" viewBox="0 0 20 20">
									<path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clip-rule="evenodd"/>
								</svg>
							</span>
							登录
						</button>
					</div>
				</form>

				<div class="mt-6">
					<div class="relative">
						<div class="absolute inset-0 flex items-center">
							<div class="w-full border-t border-gray-300"/>
						</div>
						<div class="relative flex justify-center text-sm">
							<span class="px-2 bg-white text-gray-500">系统信息</span>
						</div>
					</div>

					<div class="mt-6 grid grid-cols-1 gap-3">
						<div class="text-center">
							<div class="text-sm text-gray-600">
								<p>🖥️ 服务器管理面板</p>
								<p class="mt-1">使用系统用户账户登录</p>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>

		<div class="mt-8 text-center">
			<p class="text-xs text-gray-500">
				© 2024 DigWis Panel. 轻量级服务器管理解决方案
			</p>
		</div>
	</body>
	</html>
}
