package version

import (
	"encoding/json"
	"fmt"
	"os"
	"runtime"
	"time"
)

// Info 版本信息结构
type Info struct {
	Version   string    `json:"version"`
	GitCommit string    `json:"git_commit"`
	BuildTime string    `json:"build_time"`
	GoVersion string    `json:"go_version"`
	Platform  string    `json:"platform"`
}

// 构建时注入的变量
var (
	Version   = "v0.3.2"      // 版本号
	GitCommit = "unknown"     // Git提交哈希
	BuildTime = "unknown"     // 构建时间
)

// Get 获取版本信息
func Get() *Info {
	return &Info{
		Version:   Version,
		GitCommit: GitCommit,
		BuildTime: BuildTime,
		GoVersion: runtime.Version(),
		Platform:  fmt.Sprintf("%s/%s", runtime.GOOS, runtime.GOARCH),
	}
}

// GetVersion 获取版本号
func GetVersion() string {
	return Version
}

// GetShortVersion 获取简短版本号（去掉v前缀）
func GetShortVersion() string {
	if len(Version) > 0 && Version[0] == 'v' {
		return Version[1:]
	}
	return Version
}

// GetFullVersion 获取完整版本信息字符串
func GetFullVersion() string {
	info := Get()
	if info.GitCommit != "unknown" && len(info.GitCommit) > 7 {
		return fmt.Sprintf("%s (%s)", info.Version, info.GitCommit[:7])
	}
	return info.Version
}

// String 实现Stringer接口
func (i *Info) String() string {
	return fmt.Sprintf("DigWis Panel %s", i.Version)
}

// ToJSON 转换为JSON字符串
func (i *Info) ToJSON() string {
	data, err := json.MarshalIndent(i, "", "  ")
	if err != nil {
		return "{}"
	}
	return string(data)
}

// LoadFromFile 从文件加载版本信息（用于读取releases/version.json）
func LoadFromFile(filename string) (*Info, error) {
	data, err := os.ReadFile(filename)
	if err != nil {
		return Get(), err // 返回默认版本信息
	}

	var versionData struct {
		Latest   string `json:"latest"`
		Versions map[string]struct {
			BuildTime string `json:"build_time"`
		} `json:"versions"`
	}

	if err := json.Unmarshal(data, &versionData); err != nil {
		return Get(), err
	}

	info := Get()
	if versionData.Latest != "" {
		info.Version = versionData.Latest
	}

	if buildInfo, exists := versionData.Versions[versionData.Latest]; exists {
		if buildInfo.BuildTime != "" {
			info.BuildTime = buildInfo.BuildTime
		}
	}

	return info, nil
}

// GetBuildInfo 获取构建信息
func GetBuildInfo() map[string]string {
	info := Get()
	return map[string]string{
		"version":    info.Version,
		"git_commit": info.GitCommit,
		"build_time": info.BuildTime,
		"go_version": info.GoVersion,
		"platform":   info.Platform,
	}
}

// IsDevVersion 检查是否是开发版本
func IsDevVersion() bool {
	return GitCommit == "unknown" || BuildTime == "unknown"
}

// GetDisplayVersion 获取用于显示的版本号
func GetDisplayVersion() string {
	if IsDevVersion() {
		return fmt.Sprintf("%s-dev", Version)
	}
	return Version
}

// GetVersionWithBuildTime 获取带构建时间的版本信息
func GetVersionWithBuildTime() string {
	info := Get()
	if info.BuildTime != "unknown" {
		if buildTime, err := time.Parse(time.RFC3339, info.BuildTime); err == nil {
			return fmt.Sprintf("%s (%s)", info.Version, buildTime.Format("2006-01-02"))
		}
	}
	return info.Version
}
