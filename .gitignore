# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib

# Test binary, built with `go test -c`
*.test

# Output of the go coverage tool, specifically when used with LiteIDE
*.out

# Dependency directories (remove the comment below to include it)
# vendor/

# Go workspace file
go.work

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Log files
*.log

# Config files with sensitive data
config.yaml
config.yml
*.env

# Build artifacts
digwis-panel
server-panel

# Release directory (keep structure but ignore binaries)
release/*.tar.gz
release/*.exe
release/digwis-panel-*

# Temporary files
tmp/
temp/
*.tmp

# Local tools
tools/

# Backup files
*.bak
*.backup

# Curl cookies and temporary files
cookies.txt
*.cookie

# Node modules and npm
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# CSS output (generated)
assets/css/output.css

# Data directory (development)
data/
*.db
*.sqlite
*.sqlite3

# GitHub Token 文件（安全）
.github-token
.git-token
*.token

# 其他敏感文件
.env.local
.env.production
config.local.*

# 旧的发布文件
releases-old/

# GitHub Token 文件（安全）
.github-token
.git-token
*.token

# 其他敏感文件
.env.local
.env.production
config.local.*
