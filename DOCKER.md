# DigWis Panel Docker 部署指南

本文档介绍如何使用Docker在Ubuntu 24.04 x86_64环境中运行DigWis Panel。

## 系统要求

- Docker Engine 20.10+
- Docker Compose 2.0+
- 至少2GB可用内存
- 至少5GB可用磁盘空间

## 快速开始

### 0. 验证配置（可选）

```bash
# 验证Docker配置文件
./validate-docker.sh
```

### 1. 使用便捷脚本（推荐）

```bash
# 构建镜像
./docker-run.sh build

# 运行生产环境
./docker-run.sh run

# 运行开发环境
./docker-run.sh dev

# 查看日志
./docker-run.sh logs

# 停止容器
./docker-run.sh stop
```

### 2. 使用Docker Compose命令

#### 生产环境

```bash
# 构建并启动
docker-compose up -d --build

# 查看日志
docker-compose logs -f

# 停止
docker-compose down
```

#### 开发环境

```bash
# 构建并启动开发环境
docker-compose -f docker-compose.dev.yml up -d --build

# 查看日志
docker-compose -f docker-compose.dev.yml logs -f

# 停止
docker-compose -f docker-compose.dev.yml down
```

## 访问地址

- **生产环境**: http://localhost:8080
- **开发环境**: http://localhost:9090

## 目录结构

```
.
├── Dockerfile              # 生产环境镜像
├── Dockerfile.dev          # 开发环境镜像
├── docker-compose.yml      # 生产环境配置
├── docker-compose.dev.yml  # 开发环境配置
├── .dockerignore           # Docker忽略文件
├── docker-run.sh           # 便捷运行脚本
└── DOCKER.md              # 本文档
```

## 环境说明

### 生产环境特性

- 基于Ubuntu 24.04 x86_64
- Go 1.23.11
- Node.js 20
- 优化的构建过程
- 最小化镜像大小
- 健康检查
- 自动重启

### 开发环境特性

- 支持代码热重载
- 挂载源代码目录
- 包含开发工具
- 调试模式启用

## 数据持久化

数据库文件存储在 `./data` 目录中，该目录会被挂载到容器内，确保数据持久化。

## 系统监控

容器可以访问宿主机的系统信息：
- `/proc` - 进程信息
- `/sys` - 系统信息
- Docker socket - 容器管理

## 端口配置

- 生产环境：8080
- 开发环境：9090

如需修改端口，请编辑对应的docker-compose文件。

## 故障排除

### 1. 容器无法启动

```bash
# 查看容器日志
docker-compose logs

# 检查容器状态
docker-compose ps
```

### 2. 端口冲突

修改docker-compose.yml中的端口映射：

```yaml
ports:
  - "8081:8080"  # 将宿主机端口改为8081
```

### 3. 权限问题

确保当前用户有Docker权限：

```bash
sudo usermod -aG docker $USER
# 重新登录或重启终端
```

### 4. 架构不匹配

如果在非x86_64架构上运行，Docker会自动进行架构转换，但性能可能受影响。

## 高级配置

### 自定义环境变量

在docker-compose.yml中添加环境变量：

```yaml
environment:
  - DEBUG=true
  - LOG_LEVEL=info
  - CUSTOM_VAR=value
```

### 网络配置

默认使用bridge网络，如需自定义网络：

```yaml
networks:
  custom_network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
```

### 资源限制

```yaml
deploy:
  resources:
    limits:
      cpus: '2.0'
      memory: 2G
    reservations:
      cpus: '1.0'
      memory: 1G
```

## 安全注意事项

1. 生产环境中建议使用HTTPS
2. 定期更新基础镜像
3. 限制容器权限
4. 使用非root用户运行应用
5. 定期备份数据目录

## 维护命令

```bash
# 清理未使用的镜像
docker image prune

# 清理所有未使用的资源
docker system prune -a

# 查看镜像大小
docker images

# 查看容器资源使用
docker stats
```
