#!/bin/bash

# Docker配置验证脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_message() {
    echo -e "${2}${1}${NC}"
}

print_message "开始验证Docker配置文件..." $BLUE

# 检查Dockerfile语法
if [ -f "Dockerfile" ]; then
    print_message "✓ Dockerfile 存在" $GREEN
    
    # 检查基础镜像
    if grep -q "FROM --platform=linux/amd64 ubuntu:24.04" Dockerfile; then
        print_message "✓ 基础镜像配置正确 (Ubuntu 24.04 x86_64)" $GREEN
    else
        print_message "✗ 基础镜像配置可能有问题" $RED
    fi
    
    # 检查Go版本
    if grep -q "GO_VERSION=1.23.11" Dockerfile; then
        print_message "✓ Go版本配置正确 (1.23.11)" $GREEN
    else
        print_message "✗ Go版本配置可能有问题" $RED
    fi
    
    # 检查Node.js版本
    if grep -q "NODE_VERSION=20" Dockerfile; then
        print_message "✓ Node.js版本配置正确 (20)" $GREEN
    else
        print_message "✗ Node.js版本配置可能有问题" $RED
    fi
else
    print_message "✗ Dockerfile 不存在" $RED
fi

# 检查docker-compose.yml
if [ -f "docker-compose.yml" ]; then
    print_message "✓ docker-compose.yml 存在" $GREEN
    
    # 检查平台配置
    if grep -q "platform: linux/amd64" docker-compose.yml; then
        print_message "✓ 平台配置正确 (linux/amd64)" $GREEN
    else
        print_message "✗ 平台配置可能有问题" $RED
    fi
    
    # 检查端口配置
    if grep -q "8080:8080" docker-compose.yml; then
        print_message "✓ 端口配置正确 (8080)" $GREEN
    else
        print_message "✗ 端口配置可能有问题" $RED
    fi
    
    # 检查健康检查
    if grep -q "healthcheck:" docker-compose.yml; then
        print_message "✓ 健康检查配置存在" $GREEN
    else
        print_message "✗ 健康检查配置缺失" $RED
    fi
else
    print_message "✗ docker-compose.yml 不存在" $RED
fi

# 检查开发环境配置
if [ -f "docker-compose.dev.yml" ]; then
    print_message "✓ docker-compose.dev.yml 存在" $GREEN
    
    # 检查开发端口
    if grep -q "9090:9090" docker-compose.dev.yml; then
        print_message "✓ 开发环境端口配置正确 (9090)" $GREEN
    else
        print_message "✗ 开发环境端口配置可能有问题" $RED
    fi
else
    print_message "✗ docker-compose.dev.yml 不存在" $RED
fi

# 检查开发环境Dockerfile
if [ -f "Dockerfile.dev" ]; then
    print_message "✓ Dockerfile.dev 存在" $GREEN
else
    print_message "✗ Dockerfile.dev 不存在" $RED
fi

# 检查.dockerignore
if [ -f ".dockerignore" ]; then
    print_message "✓ .dockerignore 存在" $GREEN
else
    print_message "✗ .dockerignore 不存在" $RED
fi

# 检查项目文件
print_message "\n检查项目文件..." $BLUE

if [ -f "go.mod" ]; then
    print_message "✓ go.mod 存在" $GREEN
else
    print_message "✗ go.mod 不存在" $RED
fi

if [ -f "package.json" ]; then
    print_message "✓ package.json 存在" $GREEN
else
    print_message "✗ package.json 不存在" $RED
fi

if [ -f "main.go" ]; then
    print_message "✓ main.go 存在" $GREEN
else
    print_message "✗ main.go 不存在" $RED
fi

# 检查数据目录
if [ -d "data" ]; then
    print_message "✓ data 目录存在" $GREEN
else
    print_message "⚠ data 目录不存在，将在运行时创建" $YELLOW
fi

print_message "\nDocker配置验证完成!" $BLUE
print_message "如果所有检查都通过，您可以使用以下命令运行项目:" $GREEN
print_message "  ./docker-run.sh build  # 构建镜像" $YELLOW
print_message "  ./docker-run.sh run    # 运行生产环境" $YELLOW
print_message "  ./docker-run.sh dev    # 运行开发环境" $YELLOW
