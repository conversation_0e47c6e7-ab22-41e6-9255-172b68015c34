# DigWis Panel Air 配置文件
# 优化版本 - 支持热重载开发

root = "."
testdata_dir = "testdata"
tmp_dir = "tmp"

[build]
  # 启动参数 - 开发环境使用9090端口
  args_bin = ["-port", "9090", "-host", "0.0.0.0", "-debug"]

  # 二进制文件路径
  bin = "./tmp/digwis-panel"

  # 构建命令 - 优化编译参数
  cmd = "go build -ldflags='-s -w' -o ./tmp/digwis-panel ."

  # 构建延迟 - 减少频繁重建
  delay = 1500

  # 排除目录 - 避免监控不必要的文件
  exclude_dir = [
    "assets",
    "tmp",
    "vendor",
    "testdata",
    "node_modules",
    "data",
    "releases",
    ".git",
    ".vscode",
    "docs"
  ]

  # 排除文件
  exclude_file = [
    ".gitignore",
    ".env",
    "README.md",
    "LICENSE"
  ]

  # 排除正则表达式 - 优化匹配规则
  exclude_regex = [
    "_test\\.go$",
    "_templ\\.go$",
    "\\.db$",
    "\\.sqlite$",
    "\\.log$",
    "\\.tmp$",
    "\\~$",
    "\\.swp$"
  ]

  # 性能优化选项
  exclude_unchanged = true
  follow_symlink = false

  # 包含的目录 - 明确指定监控目录
  include_dir = [
    "internal",
    "cmd",
    "pkg"
  ]

  # 包含的文件扩展名
  include_ext = ["go", "templ", "mod", "sum"]

  # 特定包含文件
  include_file = ["main.go"]

  # 进程管理
  kill_delay = "3s"
  send_interrupt = true
  stop_on_root = false

  # 日志文件
  log = "tmp/build-errors.log"

  # 轮询设置 - 使用文件系统事件而非轮询
  poll = false
  poll_interval = 0

  # 预处理命令 - 自动生成模板和CSS
  pre_cmd = [
    "echo '🔨 生成模板文件...'",
    "go run github.com/a-h/templ/cmd/templ@latest generate",
    "echo '🎨 构建CSS文件...'",
    "npm run build-css-prod"
  ]

  # 后处理命令
  post_cmd = [
    "echo '✅ 构建完成！服务器已启动在 http://localhost:9090'"
  ]

  # 重运行设置
  rerun = false
  rerun_delay = 800

# 颜色配置 - 美化终端输出
[color]
  app = "blue"
  build = "yellow"
  main = "magenta"
  runner = "green"
  watcher = "cyan"

# 日志配置
[log]
  main_only = false
  time = true

# 杂项配置
[misc]
  clean_on_exit = true

# 屏幕配置 - 优化开发体验
[screen]
  clear_on_rebuild = true
  keep_scroll = false

# 代理配置 - 如果需要的话
[proxy]
  enabled = false
  proxy_port = 9091
  app_port = 9090
