version: '3.8'

services:
  digwis-panel-dev:
    build:
      context: .
      dockerfile: Dockerfile.dev
      platforms:
        - linux/amd64
    container_name: digwis-panel-dev
    ports:
      - "9090:9090"
    volumes:
      # 挂载源代码以支持热重载
      - .:/app
      - /app/node_modules
      # 持久化数据库文件
      - ./data:/app/data
      # 系统信息访问
      - /proc:/host/proc:ro
      - /sys:/host/sys:ro
      - /var/run/docker.sock:/var/run/docker.sock:ro
    environment:
      - HOST=0.0.0.0
      - PORT=9090
      - DEBUG=true
      - CGO_ENABLED=1
    restart: unless-stopped
    platform: linux/amd64
    network_mode: bridge
    # 开发模式下的启动命令
    command: ["npm", "run", "dev"]
    # 工作目录
    working_dir: /app

networks:
  default:
    driver: bridge
