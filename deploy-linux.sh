#!/bin/bash

# DigWis Panel Linux部署脚本

set -e

echo "🚀 开始部署 DigWis Panel..."

# 检查Go环境
if ! command -v go &> /dev/null; then
    echo "❌ Go未安装，请先安装Go 1.21+"
    exit 1
fi

# 检查Node.js环境
if ! command -v node &> /dev/null; then
    echo "❌ Node.js未安装，请先安装Node.js"
    exit 1
fi

# 安装依赖
echo "📦 安装Go依赖..."
go mod download

echo "📦 安装Node.js依赖..."
npm install

# 构建前端资源（跳过templ，因为有问题）
echo "🎨 构建CSS..."
npm run build-css-prod || echo "⚠️  CSS构建失败，继续..."

# 构建Go应用
echo "🔨 构建Go应用..."
CGO_ENABLED=1 go build -o digwis-panel .

# 创建必要的目录
echo "📁 创建目录..."
mkdir -p data
mkdir -p logs

# 设置权限
chmod +x digwis-panel

echo "✅ 部署完成！"
echo ""
echo "🎯 启动应用："
echo "  ./digwis-panel"
echo ""
echo "🌐 访问地址："
echo "  http://localhost:8080"
echo ""
echo "📝 日志文件："
echo "  logs/app.log"
