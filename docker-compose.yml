version: '3.8'

services:
  digwis-panel:
    build:
      context: .
      dockerfile: Dockerfile
      platforms:
        - linux/amd64
    container_name: digwis-panel
    ports:
      - "8080:8080"
    volumes:
      # 持久化数据库文件
      - ./data:/app/data
      # 如果需要访问宿主机的系统信息，可以挂载相关目录
      - /proc:/host/proc:ro
      - /sys:/host/sys:ro
      - /var/run/docker.sock:/var/run/docker.sock:ro
    environment:
      - HOST=0.0.0.0
      - PORT=8080
      - DEBUG=false
    restart: unless-stopped
    # 确保容器运行在x86_64架构上
    platform: linux/amd64
    # 网络模式
    network_mode: bridge
    # 健康检查
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

networks:
  default:
    driver: bridge
