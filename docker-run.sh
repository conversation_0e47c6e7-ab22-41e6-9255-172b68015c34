#!/bin/bash

# DigWis Panel Docker 运行脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_message() {
    echo -e "${2}${1}${NC}"
}

# 显示帮助信息
show_help() {
    echo "DigWis Panel Docker 运行脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  build       构建生产环境镜像"
    echo "  run         运行生产环境容器"
    echo "  dev         运行开发环境容器"
    echo "  stop        停止所有容器"
    echo "  clean       清理容器和镜像"
    echo "  logs        查看容器日志"
    echo "  shell       进入容器shell"
    echo "  help        显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 build     # 构建镜像"
    echo "  $0 run       # 运行生产环境"
    echo "  $0 dev       # 运行开发环境"
    echo "  $0 logs      # 查看日志"
}

# 检查Docker是否安装
check_docker() {
    if ! command -v docker &> /dev/null; then
        print_message "错误: Docker 未安装" $RED
        exit 1
    fi

    # 检查Docker Compose（优先使用新版本的 docker compose）
    if docker compose version &> /dev/null; then
        DOCKER_COMPOSE="docker compose"
    elif command -v docker-compose &> /dev/null; then
        DOCKER_COMPOSE="docker-compose"
    else
        print_message "错误: Docker Compose 未安装" $RED
        exit 1
    fi
}

# 构建镜像
build_image() {
    print_message "构建生产环境镜像..." $BLUE
    $DOCKER_COMPOSE build --no-cache
    print_message "镜像构建完成!" $GREEN
}

# 运行生产环境
run_production() {
    print_message "启动生产环境容器..." $BLUE
    $DOCKER_COMPOSE up -d
    print_message "生产环境已启动!" $GREEN
    print_message "访问地址: http://localhost:8080" $YELLOW
}

# 运行开发环境
run_development() {
    print_message "启动开发环境容器..." $BLUE
    $DOCKER_COMPOSE -f docker-compose.dev.yml up -d
    print_message "开发环境已启动!" $GREEN
    print_message "访问地址: http://localhost:9090" $YELLOW
}

# 停止容器
stop_containers() {
    print_message "停止所有容器..." $BLUE
    $DOCKER_COMPOSE down
    $DOCKER_COMPOSE -f docker-compose.dev.yml down
    print_message "容器已停止!" $GREEN
}

# 清理容器和镜像
clean_all() {
    print_message "清理容器和镜像..." $BLUE
    $DOCKER_COMPOSE down --rmi all --volumes --remove-orphans
    $DOCKER_COMPOSE -f docker-compose.dev.yml down --rmi all --volumes --remove-orphans
    print_message "清理完成!" $GREEN
}

# 查看日志
show_logs() {
    echo "选择要查看的日志:"
    echo "1) 生产环境"
    echo "2) 开发环境"
    read -p "请选择 (1-2): " choice

    case $choice in
        1)
            $DOCKER_COMPOSE logs -f
            ;;
        2)
            $DOCKER_COMPOSE -f docker-compose.dev.yml logs -f
            ;;
        *)
            print_message "无效选择" $RED
            ;;
    esac
}

# 进入容器shell
enter_shell() {
    echo "选择要进入的容器:"
    echo "1) 生产环境"
    echo "2) 开发环境"
    read -p "请选择 (1-2): " choice
    
    case $choice in
        1)
            docker exec -it digwis-panel /bin/bash
            ;;
        2)
            docker exec -it digwis-panel-dev /bin/bash
            ;;
        *)
            print_message "无效选择" $RED
            ;;
    esac
}

# 主逻辑
main() {
    check_docker
    
    case "${1:-help}" in
        build)
            build_image
            ;;
        run)
            run_production
            ;;
        dev)
            run_development
            ;;
        stop)
            stop_containers
            ;;
        clean)
            clean_all
            ;;
        logs)
            show_logs
            ;;
        shell)
            enter_shell
            ;;
        help|*)
            show_help
            ;;
    esac
}

main "$@"
