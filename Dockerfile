# 使用Ubuntu 24.04作为基础镜像，指定x86_64架构
FROM --platform=linux/amd64 ubuntu:24.04

# 设置环境变量
ENV DEBIAN_FRONTEND=noninteractive
ENV GO_VERSION=1.23.11
ENV NODE_VERSION=20

# 使用国内镜像源加速
RUN sed -i 's@//.*archive.ubuntu.com@//mirrors.ustc.edu.cn@g' /etc/apt/sources.list.d/ubuntu.sources

# 更新包管理器并安装基础依赖
RUN apt-get update && apt-get install -y \
    curl \
    wget \
    git \
    build-essential \
    ca-certificates \
    gnupg \
    lsb-release \
    && rm -rf /var/lib/apt/lists/*

# 安装Go
RUN wget https://go.dev/dl/go${GO_VERSION}.linux-amd64.tar.gz \
    && tar -C /usr/local -xzf go${GO_VERSION}.linux-amd64.tar.gz \
    && rm go${GO_VERSION}.linux-amd64.tar.gz

# 安装Node.js
RUN curl -fsSL https://deb.nodesource.com/setup_${NODE_VERSION}.x | bash - \
    && apt-get install -y nodejs

# 设置Go环境变量
ENV PATH="/usr/local/go/bin:${PATH}"
ENV GOPATH="/go"
ENV GOBIN="/go/bin"
ENV PATH="${GOBIN}:${PATH}"
ENV GOPROXY="https://goproxy.cn,direct"
ENV GOSUMDB="sum.golang.google.cn"

# 创建工作目录
WORKDIR /app

# 复制Go模块文件
COPY go.mod go.sum ./

# 下载Go依赖
RUN go mod download

# 复制package.json和package-lock.json
COPY package*.json ./

# 安装Node.js依赖
RUN npm ci --only=production

# 复制项目文件
COPY . .

# 创建数据目录
RUN mkdir -p /app/data

# 构建前端资源 (暂时跳过，因为templ文件有问题)
# RUN npm run build-css-prod

# 构建Go应用
RUN CGO_ENABLED=1 GOOS=linux GOARCH=amd64 go build -a -installsuffix cgo -o digwis-panel .

# 暴露端口
EXPOSE 8080

# 设置启动命令
CMD ["./digwis-panel", "-host", "0.0.0.0", "-port", "8080"]
