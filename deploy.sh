#!/bin/bash

# DigWis Panel 开发部署脚本
# 用于本地编译、上传和部署到指定服务器
# 使用方法: ./deploy.sh [选项]

set -e

# 默认配置
VERBOSE=false
QUIET=false
BUILD_ONLY=false
UPLOAD_ONLY=false
FORCE_RESTART=false
BACKUP_BEFORE_DEPLOY=true

# 服务器配置
SERVER_HOST=""
SERVER_USER="root"
SERVER_PASSWORD=""
SERVER_PORT="22"
INSTALL_DIR="/opt/digwis-panel"

# 本地配置
LOCAL_BUILD_DIR="./releases"
VERSION=""
ARCH="amd64"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        --server|-s)
            SERVER_HOST="$2"
            shift 2
            ;;
        --user|-u)
            SERVER_USER="$2"
            shift 2
            ;;
        --password|-p)
            SERVER_PASSWORD="$2"
            shift 2
            ;;
        --port)
            SERVER_PORT="$2"
            shift 2
            ;;
        --version|-v)
            VERSION="$2"
            shift 2
            ;;
        --arch|-a)
            ARCH="$2"
            shift 2
            ;;
        --build-only)
            BUILD_ONLY=true
            shift
            ;;
        --upload-only)
            UPLOAD_ONLY=true
            shift
            ;;
        --force-restart)
            FORCE_RESTART=true
            shift
            ;;
        --no-backup)
            BACKUP_BEFORE_DEPLOY=false
            shift
            ;;
        --verbose)
            VERBOSE=true
            shift
            ;;
        --quiet)
            QUIET=true
            shift
            ;;
        --help|-h)
            echo "DigWis Panel 开发部署脚本"
            echo ""
            echo "使用方法:"
            echo "  ./deploy.sh --server ************* --password mypassword"
            echo ""
            echo "必需参数:"
            echo "  --server, -s HOST      目标服务器地址"
            echo "  --password, -p PASS    服务器密码"
            echo ""
            echo "可选参数:"
            echo "  --user, -u USER        服务器用户名 (默认: root)"
            echo "  --port PORT            SSH端口 (默认: 22)"
            echo "  --version, -v VER      指定版本号 (默认: 自动检测)"
            echo "  --arch, -a ARCH        目标架构 (默认: amd64)"
            echo "  --build-only           仅编译，不上传部署"
            echo "  --upload-only          仅上传部署，不编译"
            echo "  --force-restart        强制重启服务"
            echo "  --no-backup            部署前不备份"
            echo "  --verbose              显示详细信息"
            echo "  --quiet                静默模式"
            echo "  --help, -h             显示此帮助信息"
            echo ""
            echo "示例:"
            echo "  ./deploy.sh -s ************* -p mypass"
            echo "  ./deploy.sh -s example.com -u admin -p mypass --version v0.3.2"
            echo "  ./deploy.sh --build-only --version v0.3.3"
            exit 0
            ;;
        *)
            echo "未知参数: $1"
            echo "使用 --help 查看帮助信息"
            exit 1
            ;;
    esac
done

# 打印函数
print_info() {
    if [ "$QUIET" != "true" ]; then
        echo -e "${BLUE}[INFO]${NC} $1"
    fi
}

print_success() {
    if [ "$QUIET" != "true" ]; then
        echo -e "${GREEN}[SUCCESS]${NC} $1"
    fi
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1" >&2
}

print_warning() {
    if [ "$QUIET" != "true" ]; then
        echo -e "${YELLOW}[WARNING]${NC} $1"
    fi
}

print_step() {
    if [ "$QUIET" != "true" ]; then
        echo -e "${YELLOW}[STEP]${NC} $1"
    fi
}

print_verbose() {
    if [ "$VERBOSE" = "true" ]; then
        echo -e "${BLUE}[VERBOSE]${NC} $1"
    fi
}

# 显示Logo
show_logo() {
    if [ "$QUIET" != "true" ]; then
        echo -e "${BLUE}"
        echo "=================================="
        echo "   DigWis Panel 开发部署脚本"
        echo "=================================="
        echo -e "${NC}"
    fi
}

# 检查必需的工具
check_dependencies() {
    print_step "检查依赖工具..."
    
    local missing_tools=()
    
    if ! command -v sshpass >/dev/null 2>&1; then
        missing_tools+=("sshpass")
    fi
    
    if ! command -v go >/dev/null 2>&1; then
        missing_tools+=("go")
    fi
    
    if ! command -v npm >/dev/null 2>&1; then
        missing_tools+=("npm")
    fi
    
    if [ ${#missing_tools[@]} -gt 0 ]; then
        print_error "缺少必需的工具: ${missing_tools[*]}"
        echo ""
        echo "请安装缺少的工具:"
        for tool in "${missing_tools[@]}"; do
            case $tool in
                sshpass)
                    echo "  macOS: brew install sshpass"
                    echo "  Ubuntu/Debian: apt install sshpass"
                    echo "  CentOS/RHEL: yum install sshpass"
                    ;;
                go)
                    echo "  访问 https://golang.org/dl/ 下载安装"
                    ;;
                npm)
                    echo "  访问 https://nodejs.org/ 下载安装"
                    ;;
            esac
        done
        exit 1
    fi
    
    print_success "依赖工具检查完成"
}

# 验证参数
validate_params() {
    print_step "验证参数..."
    
    if [ "$BUILD_ONLY" = "false" ] && [ "$UPLOAD_ONLY" = "false" ]; then
        if [ -z "$SERVER_HOST" ]; then
            print_error "必须指定服务器地址 (--server)"
            exit 1
        fi
        
        if [ -z "$SERVER_PASSWORD" ]; then
            print_error "必须指定服务器密码 (--password)"
            exit 1
        fi
    fi
    
    if [ "$UPLOAD_ONLY" = "true" ] && [ -z "$VERSION" ]; then
        print_error "仅上传模式必须指定版本号 (--version)"
        exit 1
    fi
    
    print_success "参数验证完成"
}

# 获取版本号
get_version() {
    if [ -z "$VERSION" ]; then
        print_step "获取版本号..."

        # 尝试从git tag获取版本
        VERSION=$(git describe --tags --abbrev=0 2>/dev/null || echo "")

        # 如果没有git tag，尝试从package.json获取版本
        if [ -z "$VERSION" ] && [ -f "package.json" ]; then
            local pkg_version=$(grep '"version"' package.json | cut -d'"' -f4)
            if [ -n "$pkg_version" ]; then
                VERSION="v$pkg_version"
            fi
        fi

        # 如果还是没有，使用默认版本
        if [ -z "$VERSION" ]; then
            VERSION="v0.3.2"
            print_warning "无法获取版本号，使用默认版本: $VERSION"
        else
            print_verbose "检测到版本号: $VERSION"
        fi
    fi

    # 确保版本号以v开头
    if [[ ! "$VERSION" =~ ^v ]]; then
        VERSION="v$VERSION"
    fi

    print_info "使用版本号: $VERSION"
}

# 本地编译
build_local() {
    print_step "本地编译..."

    # 检查是否存在构建脚本
    if [ ! -f "./build_release.sh" ]; then
        print_error "未找到构建脚本 build_release.sh"
        exit 1
    fi

    print_verbose "执行构建脚本..."
    if [ "$VERBOSE" = "true" ]; then
        ./build_release.sh
    else
        ./build_release.sh >/dev/null 2>&1
    fi

    # 验证构建结果
    local package_name="digwis-panel-${VERSION}-linux-${ARCH}.tar.gz"
    local package_path="${LOCAL_BUILD_DIR}/${VERSION}/${package_name}"

    if [ ! -f "$package_path" ]; then
        print_error "构建失败，未找到: $package_path"
        exit 1
    fi

    print_success "本地编译完成: $package_path"
}

# 上传文件到服务器
upload_to_server() {
    print_step "上传文件到服务器..."

    local package_name="digwis-panel-${VERSION}-linux-${ARCH}.tar.gz"
    local package_path="${LOCAL_BUILD_DIR}/${VERSION}/${package_name}"
    local remote_path="/tmp/${package_name}"

    if [ ! -f "$package_path" ]; then
        print_error "未找到构建文件: $package_path"
        exit 1
    fi

    print_verbose "上传 $package_path 到 ${SERVER_USER}@${SERVER_HOST}:${remote_path}"

    if [ "$VERBOSE" = "true" ]; then
        sshpass -p "$SERVER_PASSWORD" scp -P "$SERVER_PORT" "$package_path" "${SERVER_USER}@${SERVER_HOST}:${remote_path}"
    else
        sshpass -p "$SERVER_PASSWORD" scp -P "$SERVER_PORT" "$package_path" "${SERVER_USER}@${SERVER_HOST}:${remote_path}" >/dev/null 2>&1
    fi

    print_success "文件上传完成"
}

# 在服务器上部署
deploy_on_server() {
    print_step "在服务器上部署..."

    local package_name="digwis-panel-${VERSION}-linux-${ARCH}.tar.gz"
    local remote_path="/tmp/${package_name}"
    local backup_dir="/tmp/digwis-panel-backup-$(date +%s)"

    # 构建部署脚本
    local deploy_script="
set -e

echo '🔍 检查服务状态...'
if systemctl is-active --quiet digwis-panel 2>/dev/null; then
    echo '⏹️  停止服务...'
    systemctl stop digwis-panel
fi

# 备份现有安装
if [ '$BACKUP_BEFORE_DEPLOY' = 'true' ] && [ -d '$INSTALL_DIR' ]; then
    echo '💾 备份现有安装...'
    cp -r '$INSTALL_DIR' '$backup_dir'
    echo '备份保存在: $backup_dir'
fi

echo '📦 解压新版本...'
cd /tmp
tar -xzf '$remote_path'

# 查找解压后的二进制文件
BINARY_FILE=\$(find . -name 'digwis-panel-*' -type f ! -name '*.tar.gz' | head -1)
if [ -z \"\$BINARY_FILE\" ]; then
    echo '❌ 解压失败或文件损坏'
    exit 1
fi

echo '📁 创建安装目录...'
mkdir -p '$INSTALL_DIR/data'
mkdir -p '/etc/digwis-panel'
mkdir -p '/var/log/digwis-panel'

echo '📋 复制程序文件...'
cp \"\$BINARY_FILE\" '$INSTALL_DIR/digwis-panel'
chmod +x '$INSTALL_DIR/digwis-panel'

# 设置权限
chown -R root:root '$INSTALL_DIR'
chmod -R 750 '$INSTALL_DIR'

echo '⚙️  更新配置文件...'
if [ ! -f '/etc/digwis-panel/config.yaml' ]; then
    cat > /etc/digwis-panel/config.yaml << 'EOF'
server:
  port: 8080
  host: \"0.0.0.0\"

auth:
  session_timeout: 3600

log:
  level: \"info\"
  file: \"/var/log/digwis-panel/digwis-panel.log\"
EOF
fi

echo '🔧 更新系统服务...'
cat > /etc/systemd/system/digwis-panel.service << 'EOF'
[Unit]
Description=DigWis Server Management Panel
After=network.target

[Service]
Type=simple
User=root
WorkingDirectory=$INSTALL_DIR
Environment=DIGWIS_MODE=production
Environment=DIGWIS_DATA_DIR=$INSTALL_DIR/data
ExecStart=$INSTALL_DIR/digwis-panel -port 8080
Restart=always
RestartSec=5
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target
EOF

systemctl daemon-reload
systemctl enable digwis-panel

echo '🚀 启动服务...'
systemctl start digwis-panel

# 等待服务启动
sleep 3

if systemctl is-active --quiet digwis-panel; then
    echo '✅ 部署成功！服务已启动'
    echo '🌐 访问地址: http://\$(hostname -I | awk '{print \$1}'):8080'
else
    echo '❌ 服务启动失败'
    echo '查看日志: journalctl -u digwis-panel -f'
    exit 1
fi

# 清理临时文件
rm -f '$remote_path'
rm -f \"\$BINARY_FILE\"

echo '🧹 清理完成'
"

    print_verbose "执行远程部署脚本..."
    if [ "$VERBOSE" = "true" ]; then
        sshpass -p "$SERVER_PASSWORD" ssh -p "$SERVER_PORT" "${SERVER_USER}@${SERVER_HOST}" "$deploy_script"
    else
        sshpass -p "$SERVER_PASSWORD" ssh -p "$SERVER_PORT" "${SERVER_USER}@${SERVER_HOST}" "$deploy_script" 2>/dev/null
    fi

    print_success "服务器部署完成"
}

# 检查服务器连接
test_connection() {
    print_step "测试服务器连接..."

    print_verbose "连接到 ${SERVER_USER}@${SERVER_HOST}:${SERVER_PORT}"

    if sshpass -p "$SERVER_PASSWORD" ssh -p "$SERVER_PORT" -o ConnectTimeout=10 "${SERVER_USER}@${SERVER_HOST}" "echo 'Connection OK'" >/dev/null 2>&1; then
        print_success "服务器连接正常"
    else
        print_error "无法连接到服务器"
        print_info "请检查:"
        print_info "  - 服务器地址: $SERVER_HOST"
        print_info "  - 端口: $SERVER_PORT"
        print_info "  - 用户名: $SERVER_USER"
        print_info "  - 密码是否正确"
        exit 1
    fi
}

# 显示部署结果
show_result() {
    if [ "$QUIET" != "true" ]; then
        echo ""
        echo -e "${GREEN}=================================="
        echo "      部署完成！"
        echo "==================================${NC}"
        echo ""
        if [ "$BUILD_ONLY" = "true" ]; then
            echo "🔨 编译完成"
            echo "📁 构建文件: ${LOCAL_BUILD_DIR}/${VERSION}/"
        elif [ "$UPLOAD_ONLY" = "true" ]; then
            echo "📤 上传部署完成"
            echo "🌐 访问地址: http://${SERVER_HOST}:8080"
        else
            echo "🚀 编译和部署完成"
            echo "🌐 访问地址: http://${SERVER_HOST}:8080"
        fi
        echo ""
        echo "🔧 管理命令 (在服务器上执行):"
        echo "   systemctl status digwis-panel   # 查看状态"
        echo "   systemctl restart digwis-panel  # 重启服务"
        echo "   journalctl -u digwis-panel -f   # 查看日志"
        echo ""
    fi
}

# 主函数
main() {
    show_logo
    validate_params
    check_dependencies
    get_version

    if [ "$UPLOAD_ONLY" = "false" ]; then
        build_local
    fi

    if [ "$BUILD_ONLY" = "false" ]; then
        test_connection
        upload_to_server
        deploy_on_server
    fi

    show_result
}

# 执行主函数
main "$@"
